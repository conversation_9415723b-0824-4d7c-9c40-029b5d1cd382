<template>
    <div id="cockpitMap">
        <div class="back-button-container">
            <a-button class="back-btn" @click="goBack">
                <template #icon><left-outlined /></template>
                返回
            </a-button>
        </div>
        <div class="view-control-container">
            <a-tooltip placement="left" v-if="route.query.file_url === '/models/pc/0/terra_b3dms/tileset.json'"
                title="切换模型">
                <a-button class="view-btn" @click="switchModel">
                    <template #icon><swap-outlined /></template>
                </a-button>
            </a-tooltip>
            <a-tooltip placement="left" title="顶视图">
                <a-button class="view-btn" @click="switchToTopView">
                    <template #icon><column-height-outlined /></template>
                </a-button>
            </a-tooltip>
            <a-tooltip placement="left" title="透视图">
                <a-button class="view-btn" @click="switchToPerspectiveView">
                    <template #icon><eye-outlined /></template>
                </a-button>
            </a-tooltip>
            <a-tooltip placement="left" title="绘制圆圈">
                <a-button class="view-btn" @click="drawCircle">
                    <template #icon><border-outlined /></template>
                </a-button>
            </a-tooltip>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { useGMapManage } from '@/hooks/use-c-map';
import { getApp, getRoot } from '@/root';
import { ColumnHeightOutlined, EyeOutlined, LeftOutlined, SwapOutlined, BorderOutlined } from '@ant-design/icons-vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import proj4 from 'proj4'

const viewer = ref(null);
const tileset = ref(null);
const glbModel = ref(null); // 添加glb模型引用
const store = useStore();
const router = useRouter();
const route = useRoute();
const currentModelUrl = ref('');  // 添加变量来跟踪当前模型
const isGlbModel = ref(false);  // 添加变量来跟踪当前是否为glb模型
const circleEntity = ref(null); // 存储绘制的圆圈实体
const webodmModelInfo = ref(null); // 存储WebODM模型信息

// 加载WebODM模型地理参考信息
const loadWebODMModelInfo = async () => {
    try {
        const response = await fetch('/models/webodmModel/odm_georeferencing/odm_georeferenced_model.info.json');
        const info = await response.json();
        webodmModelInfo.value = info;
        return info;
    } catch (error) {
        console.error('Failed to load WebODM model info:', error);
        return null;
    }
};

// 返回上一级页面
const goBack = () => {
    router.back();
};

// 计算模型的实际中心点（考虑变换矩阵）
const getModelCenter = () => {
    if (isGlbModel.value && glbModel.value) {
        // 对于GLB模型，使用其包围球的中心
        return glbModel.value.boundingSphere.center;
    } else if (!isGlbModel.value && tileset.value) {
        const boundingSphere = tileset.value.boundingSphere;
        const modelMatrix = tileset.value.modelMatrix;

        // 如果有模型变换矩阵，需要应用变换
        if (modelMatrix && !Cesium.Matrix4.equals(modelMatrix, Cesium.Matrix4.IDENTITY)) {
            return Cesium.Matrix4.multiplyByPoint(modelMatrix, boundingSphere.center, new Cesium.Cartesian3());
        }

        return boundingSphere.center;
    }

    return null;
};

// 绘制圆圈
const drawCircle = (color = '#ccae62') => {
    if (!viewer.value) return;

    // 如果已经有圆圈，先移除
    if (circleEntity.value) {
        viewer.value.entities.remove(circleEntity.value);
        circleEntity.value = null;
        return;
    }

    const modelCenter = getModelCenter();
    if (!modelCenter) return;

    // 获取模型中心的笛卡尔坐标
    const cartographic = Cesium.Cartographic.fromCartesian(modelCenter);
    const centerLongitude = cartographic.longitude;
    const centerLatitude = cartographic.latitude;

    // 确保颜色是字符串类型
    const colorString = typeof color === 'string' ? color : '#ccae62';

    // 创建Cesium颜色对象
    let mainColor, borderColor;
    mainColor = Cesium.Color.fromCssColorString(colorString).withAlpha(0.5);
    borderColor = Cesium.Color.fromCssColorString(colorString).brighten(-0.3, new Cesium.Color());

    // 创建一个圆形实体，并设置材质
    circleEntity.value = viewer.value.entities.add({
        position: Cesium.Cartesian3.fromRadians(centerLongitude, centerLatitude, cartographic.height),
        name: '模型中心圆',
        // 使用椭圆实体，因为Cesium中的圆是椭圆的特例
        ellipse: {
            semiMinorAxis: 100, // 100米半径
            semiMajorAxis: 100, // 100米半径
            material: new Cesium.ColorMaterialProperty(mainColor),
            outline: true,
            outlineColor: borderColor,
            outlineWidth: 30,
            // 确保圆圈采样足够的点以贴合起伏地形
            granularity: Cesium.Math.RADIANS_PER_DEGREE / 4, // 每度切分4个点
            classificationType: Cesium.ClassificationType.BOTH // 同时贴合地形和3D Tiles
        }
    });

    // 缩放视图以查看圆圈
    viewer.value.zoomTo(circleEntity.value);
};

// 切换到顶视图
const switchToTopView = () => {
    if (!viewer.value) return;
    if ((!tileset.value && !glbModel.value) || !getModelCenter()) return;

    const modelCenter = getModelCenter();
    if (!modelCenter) return;

    const cartographic = Cesium.Cartographic.fromCartesian(modelCenter);
    const boundingSphere = isGlbModel.value ? glbModel.value.boundingSphere : tileset.value.boundingSphere;

    // 计算合适的高度，确保整个模型可见
    const radius = boundingSphere.radius;
    const height = Math.max(radius * 2, 300);

    viewer.value.camera.flyTo({
        destination: Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            cartographic.height + height
        ),
        orientation: {
            heading: 0.0,
            pitch: -Cesium.Math.PI_OVER_TWO, // -90度，直视下方
            roll: 0.0
        },
        duration: 1.5, // 2秒飞行时间，避免乱飞跃
    });
};

// 切换到透视图
const switchToPerspectiveView = () => {
    if (!viewer.value) return;
    if ((!tileset.value && !glbModel.value) || !getModelCenter()) return;

    const modelCenter = getModelCenter();
    if (!modelCenter) return;

    const cartographic = Cesium.Cartographic.fromCartesian(modelCenter);
    const boundingSphere = isGlbModel.value ? glbModel.value.boundingSphere : tileset.value.boundingSphere;

    // 计算合适的高度和距离，确保整个模型可见
    const radius = boundingSphere.radius;
    const height = Math.max(radius * 1.5, 200);
    const distance = height * 1.2; // 水平距离偏移量

    // 计算相机位置：向西南方向偏移以便从西南角观察模型
    const cameraPosition = Cesium.Cartesian3.fromRadians(
        cartographic.longitude - Cesium.Math.toRadians(distance / 111000 / Math.sqrt(2)), // 向西偏移
        cartographic.latitude - Cesium.Math.toRadians(distance / 111000 / Math.sqrt(2)), // 向南偏移
        cartographic.height + height
    );

    viewer.value.camera.flyTo({
        destination: cameraPosition,
        orientation: {
            heading: Cesium.Math.toRadians(45), // 从西南角观察（朝向东北方向）
            pitch: Cesium.Math.toRadians(-45), // -45度俯角
            roll: 0.0
        },
        duration: 1.5
    });
};

// 加载WebODM GLB模型（带地理参考）
const loadWebODMGlbModel = async (url) => {
    if (!viewer.value) return;

    // 移除现有的glb模型（如果有）
    if (glbModel.value) {
        viewer.value.scene.primitives.remove(glbModel.value);
        glbModel.value = null;
    }

    // 加载WebODM模型信息
    const modelInfo = await loadWebODMModelInfo();
    if (!modelInfo) {
        console.error('Failed to load WebODM model info');
        return;
    }

    // 从模型信息中获取WGS84边界框
    const bbox = modelInfo.stats.bbox['EPSG:4326'].bbox;
    const centerLon = (bbox.minx + bbox.maxx) / 2;
    const centerLat = (bbox.miny + bbox.maxy) / 2;
    const centerHeight = (bbox.minz + bbox.maxz) / 2;

    console.log('WebODM Model Center:', { centerLon, centerLat, centerHeight });

    // 创建模型位置
    const position = Cesium.Cartesian3.fromDegrees(centerLon, centerLat, centerHeight);

    // 创建模型变换矩阵（不旋转，保持原始方向）
    const modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(position);

    glbModel.value = Cesium.Model.fromGltf({
        url: url,
        modelMatrix: modelMatrix,
        scale: 1.0,
        minimumPixelSize: 128,
        maximumScale: 20000,
        allowPicking: true
    });

    viewer.value.scene.primitives.add(glbModel.value);

    // 监听模型加载完成并缩放到合适位置
    glbModel.value.readyPromise.then(function (model) {
        // 计算合适的视角距离
        const boundingSphere = model.boundingSphere;
        const radius = boundingSphere.radius;
        const distance = radius * 3.0; // 距离为半径的3倍

        viewer.value.camera.flyToBoundingSphere(boundingSphere, {
            offset: new Cesium.HeadingPitchRange(
                Cesium.Math.toRadians(45), // 45度方位角
                Cesium.Math.toRadians(-30), // -30度俯仰角
                distance
            ),
            duration: 2.0
        });
    });
};

// 加载普通GLB模型
const loadGlbModel = (url) => {
    if (!viewer.value) return;

    // 移除现有的glb模型（如果有）
    if (glbModel.value) {
        viewer.value.scene.primitives.remove(glbModel.value);
        glbModel.value = null;
    }

    // 加载GLB模型
    const position = Cesium.Cartesian3.fromDegrees(114.31, 30.57, -10); // 将高度设为负值，让模型位于地面以下
    const headingPitchRoll = new Cesium.HeadingPitchRoll(0, -Math.PI / 2, 0); // 调整pitch为-90度，将竖直模型放平
    const modelMatrix = Cesium.Transforms.headingPitchRollToFixedFrame(
        position,
        headingPitchRoll
    );

    glbModel.value = Cesium.Model.fromGltf({
        url: url,
        modelMatrix: modelMatrix,
        scale: 1.0, // 可根据需要调整模型大小
        minimumPixelSize: 128,
        maximumScale: 20000,
        allowPicking: true
    });

    viewer.value.scene.primitives.add(glbModel.value);

    // 监听模型加载完成并缩放到合适位置
    glbModel.value.readyPromise.then(function (model) {
        viewer.value.camera.flyToBoundingSphere(model.boundingSphere, {
            offset: new Cesium.HeadingPitchRange(0, -Math.PI / 4, model.boundingSphere.radius * 2.0)
        });
    });
};

// 加载3D Tiles模型
const load3DTilesModel = (url) => {
    if (!viewer.value) return;

    // 移除现有的tileset（如果有）
    if (tileset.value) {
        viewer.value.scene.primitives.remove(tileset.value);
        tileset.value = null;
    }

    // 加载3D Tiles模型
    tileset.value = viewer.value.scene.primitives.add(
        new Cesium.Cesium3DTileset({
            url: url,
            maximumScreenSpaceError: 1.0,
            dynamicScreenSpaceError: true,
            dynamicScreenSpaceErrorFactor: 4.0,
            dynamicScreenSpaceErrorDensity: 0.00278,
            preferLeaves: true
        })
    );

    // 监听模型加载完成并缩放到合适位置
    tileset.value.readyPromise.then(function (tilesetLoaded) {
        viewer.value.zoomTo(tilesetLoaded);
    });
};

// 切换模型
const switchModel = () => {
    if (!viewer.value) return;

    // 在两个模型之间切换
    const originalModel = route.query.file_url;
    const alternateModel = '/models/pc/0/terra_pnts/tileset.json';

    // 如果当前是原始模型，则切换到替代模型，反之亦然
    currentModelUrl.value = currentModelUrl.value === alternateModel ? originalModel : alternateModel;

    // 加载选定的模型
    load3DTilesModel(currentModelUrl.value);
    isGlbModel.value = false;
};

// 加载模型（根据文件扩展名决定如何加载）
const loadModel = (url) => {
    if (!url) return;

    // 检查是否为WebODM模型
    if (url.includes('webodmModel')) {
        // 检查文件类型
        const fileExtension = url.split('.').pop().toLowerCase();

        if (fileExtension === 'glb') {
            // 加载WebODM GLB模型（带地理参考）
            loadWebODMGlbModel(url);
            isGlbModel.value = true;
        } else {
            // 对于其他WebODM文件类型，暂时使用普通3D Tiles加载
            load3DTilesModel(url);
            isGlbModel.value = false;
        }
    } else {
        // 检查文件类型
        const fileExtension = url.split('.').pop().toLowerCase();

        if (fileExtension === 'glb') {
            // 加载普通GLB模型
            loadGlbModel(url);
            isGlbModel.value = true;
        } else {
            // 加载3D Tiles模型
            load3DTilesModel(url);
            isGlbModel.value = false;
        }
    }

    currentModelUrl.value = url;
};

onMounted(async () => {
    const app = getApp();

    // 检查是否为WebODM模型，如果是则使用模型的地理中心作为地图中心
    let mapCenter = [114.31, 30.57]; // 默认中心

    if (route.query.file_url && route.query.file_url.includes('webodmModel')) {
        try {
            const modelInfo = await loadWebODMModelInfo();
            if (modelInfo && modelInfo.stats && modelInfo.stats.bbox && modelInfo.stats.bbox['EPSG:4326']) {
                const bbox = modelInfo.stats.bbox['EPSG:4326'].bbox;
                const centerLon = (bbox.minx + bbox.maxx) / 2;
                const centerLat = (bbox.miny + bbox.maxy) / 2;
                mapCenter = [centerLon, centerLat];
                console.log('Using WebODM model center for map initialization:', mapCenter);
            }
        } catch (error) {
            console.warn('Failed to load WebODM model info for map center, using default:', error);
        }
    }

    await useGMapManage().globalPropertiesConfig(app, mapCenter, 'cockpitMap');
    viewer.value = app.config.globalProperties.$map;

    // 设置当前模型URL为初始URL
    currentModelUrl.value = route.query.file_url;

    // 根据URL加载合适的模型
    loadModel(currentModelUrl.value);
});
</script>

<style lang="scss" scoped>
#cockpitMap {
    width: 100%;
    height: 100%;
    position: relative;
}

:deep(.cesium-viewer-bottom) {
    display: none;
}

.back-button-container {
    position: absolute;
    left: 20px;
    top: 20px;
    z-index: 999;
}

.back-btn {
    height: 36px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;

    &:hover {
        background-color: #f5f5f5;
    }
}

.view-control-container {
    position: absolute;
    right: 20px;
    bottom: 20px;
    z-index: 999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.view-btn {
    width: 40px;
    height: 40px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background-color: #f5f5f5;
    }
}
</style>